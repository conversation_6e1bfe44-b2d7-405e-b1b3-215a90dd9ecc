import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  serial,
  integer,
  boolean,
  uniqueIndex
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";
import { randomUUID } from "crypto";

// Session storage table.
// (IMPORTANT) This table is mandatory for Replit Auth, don't drop it.
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User storage table.
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull().$defaultFn(() => randomUUID()),
  email: varchar("email").unique().notNull(),
  password: varchar("password").notNull(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Chat conversations table
export const chats = pgTable("chats", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull().references(() => users.id),
  title: varchar("title").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Chat messages table
export const messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  chatId: integer("chat_id").notNull().references(() => chats.id),
  userId: varchar("user_id").notNull().references(() => users.id), // Add userId field
  content: text("content").notNull(),
  role: varchar("role").notNull(), // 'user' or 'assistant'
  createdAt: timestamp("created_at").defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  chats: many(chats),
  messages: many(messages),
}));

export const chatsRelations = relations(chats, ({ one, many }) => ({
  user: one(users, {
    fields: [chats.userId],
    references: [users.id],
  }),
  messages: many(messages),
}));

export const messagesRelations = relations(messages, ({ one }) => ({
  chat: one(chats, {
    fields: [messages.chatId],
    references: [chats.id],
  }),
  user: one(users, {
    fields: [messages.userId],
    references: [users.id],
  }),
}));


export const embeddings = pgTable('embeddings', {
  id: serial('id').primaryKey(),
  user_input: text('user_input').notNull(),
  bot_output: text('bot_output').notNull(),
  embedding: text('embedding').notNull(),
  created_at: timestamp('created_at').defaultNow(),
  userId: varchar('user_id').notNull().references(() => users.id), // NEW FIELD
});

// User context table for storing user information across chats
export const userContext = pgTable('user_context', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id').notNull().references(() => users.id),
  contextKey: varchar('context_key').notNull(), // e.g., 'name', 'preferences', 'interests'
  contextValue: text('context_value').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Relationships table for storing entity relationships
export const relationships = pgTable('relationships', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id').notNull().references(() => users.id),
  entity1: varchar('entity1').notNull(),
  relationship: varchar('relationship').notNull(),
  entity2: varchar('entity2').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => {
  return {
    userIdIdx: index('idx_relationships_user_id').on(table.userId),
    entity1Idx: index('idx_relationships_entity1').on(table.entity1),
    entity2Idx: index('idx_relationships_entity2').on(table.entity2),
  };
});

// Relations
export const userContextRelations = relations(userContext, ({ one }) => ({
  user: one(users, {
    fields: [userContext.userId],
    references: [users.id],
  }),
}));

export const relationshipsRelations = relations(relationships, ({ one }) => ({
  user: one(users, {
    fields: [relationships.userId],
    references: [users.id],
  }),
}));

// Types
export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;

export type InsertChat = typeof chats.$inferInsert;
export type Chat = typeof chats.$inferSelect;

export type InsertMessage = typeof messages.$inferInsert;
export type Message = typeof messages.$inferSelect;

export type InsertUserContext = typeof userContext.$inferInsert;
export type UserContext = typeof userContext.$inferSelect;

export type InsertRelationship = typeof relationships.$inferInsert;
export type Relationship = typeof relationships.$inferSelect;

export type InsertPsychologyQuestion = typeof psychologyQuestions.$inferInsert;
export type PsychologyQuestion = typeof psychologyQuestions.$inferSelect;

export type InsertUserGeneratedQuestion = typeof userGeneratedQuestions.$inferInsert;
export type UserGeneratedQuestion = typeof userGeneratedQuestions.$inferSelect;

// Schemas
export const insertChatSchema = createInsertSchema(chats).omit({
  id: true,
  userId: true,  // Remove userId from required fields in request body
  createdAt: true,
  updatedAt: true,
});


export const insertMessageSchema = createInsertSchema(messages).omit({
  id: true,
  createdAt: true,
});

// Extended types with relations
export type ChatWithMessages = Chat & {
  messages: Message[];
};

export type ChatWithUser = Chat & {
  user: User;
};

// Psychology questions table
export const psychologyQuestions = pgTable('psychology_questions', {
  id: serial('id').primaryKey(),
  question: text('question').notNull(),
  category: varchar('category').notNull(), // e.g., 'initial_assessment', 'coping_mechanisms', etc.
  isActive: boolean('is_active').default(true),
  orderIndex: integer('order_index').default(0),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => {
  return {
    categoryIdx: index('idx_psychology_questions_category').on(table.category),
    activeIdx: index('idx_psychology_questions_active').on(table.isActive),
  };
});

// User-specific generated questions table
export const userGeneratedQuestions = pgTable('user_generated_questions', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id').notNull().references(() => users.id),
  question: text('question').notNull(),
  category: varchar('category').notNull(),
  isUsed: boolean('is_used').default(false),
  usedAt: timestamp('used_at'),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => {
  return {
    userIdIdx: index('idx_user_generated_questions_user_id').on(table.userId),
    categoryIdx: index('idx_user_generated_questions_category').on(table.category),
    usedIdx: index('idx_user_generated_questions_used').on(table.isUsed),
  };
});

// Psychology questions relations
export const psychologyQuestionsRelations = relations(psychologyQuestions, ({ many }) => ({
  userGeneratedQuestions: many(userGeneratedQuestions),
}));

export const userGeneratedQuestionsRelations = relations(userGeneratedQuestions, ({ one }) => ({
  user: one(users, {
    fields: [userGeneratedQuestions.userId],
    references: [users.id],
  }),
}));
