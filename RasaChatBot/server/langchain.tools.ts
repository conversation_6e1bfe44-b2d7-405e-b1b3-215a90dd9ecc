import { Chat<PERSON>penAI } from "@langchain/openai";
import { 
  DynamicTool,
  Tool
} from "@langchain/core/tools";
import { 
  CallbackManager
} from "@langchain/core/callbacks/manager";
import { getLangChainConfig } from './langchain.config';
import { storage } from './storage';

const config = getLangChainConfig();

// Initialize LLM for tools
const toolLLM = new ChatOpenAI({
  openAIApiKey: process.env.OPENAI_API_KEY,
  modelName: config.modelName,
  temperature: 0.1, // Lower temperature for tools
  streaming: false,
});

// Callback manager for logging
export const callbackManager = new CallbackManager();

// Tool for getting user context
export const getUserContextTool = new DynamicTool({
  name: "get_user_context",
  description: "Get stored user context information like name, age, location, preferences, etc.",
  func: async (userId: string) => {
    try {
      const userContexts = await storage.getAllUserContext(userId);
      if (userContexts.length === 0) {
        return "No user context information found.";
      }
      
      const contextString = userContexts
        .map(ctx => `${ctx.key}: ${ctx.value}`)
        .join(', ');
      
      return `User context: ${contextString}`;
    } catch (error) {
      console.error('Error getting user context:', error);
      return "Error retrieving user context.";
    }
  },
});

// Tool for setting user context
export const setUserContextTool = new DynamicTool({
  name: "set_user_context",
  description: "Store user context information like name, age, location, preferences, etc.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:key:value"
      const [userId, key, value] = input.split(':');
      if (!userId || !key || !value) {
        return "Invalid format. Expected: userId:key:value";
      }
      
      await storage.setUserContext(userId, key, value);
      return `Successfully stored ${key}: ${value} for user ${userId}`;
    } catch (error) {
      console.error('Error setting user context:', error);
      return "Error storing user context.";
    }
  },
});

// Tool for searching similar conversations
export const searchSimilarConversationsTool = new DynamicTool({
  name: "search_similar_conversations",
  description: "Search for similar conversations based on user input to provide context-aware responses.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:query:limit"
      const [userId, query, limitStr] = input.split(':');
      const limit = parseInt(limitStr) || 3;
      
      if (!userId || !query) {
        return "Invalid format. Expected: userId:query:limit";
      }
      
      const similarEmbeddings = await storage.getSimilarEmbeddings(userId, query, limit);
      if (similarEmbeddings.length === 0) {
        return "No similar conversations found.";
      }
      
      const contextSnippets = similarEmbeddings
        .map(s => `User: ${s.user_input}\nAssistant: ${s.bot_output}`)
        .join('\n\n');
      
      return `Similar conversations:\n${contextSnippets}`;
    } catch (error) {
      console.error('Error searching similar conversations:', error);
      return "Error searching similar conversations.";
    }
  },
});

// Tool for getting chat history
export const getChatHistoryTool = new DynamicTool({
  name: "get_chat_history",
  description: "Get the conversation history for a specific chat.",
  func: async (chatId: string) => {
    try {
      const chat = await storage.getChatWithMessages(parseInt(chatId));
      if (!chat) {
        return "Chat not found.";
      }
      
      const history = chat.messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');
      
      return `Chat history:\n${history}`;
    } catch (error) {
      console.error('Error getting chat history:', error);
      return "Error retrieving chat history.";
    }
  },
});

// Tool for generating summaries
export const generateSummaryTool = new DynamicTool({
  name: "generate_summary",
  description: "Generate a summary of the conversation or text content.",
  func: async (content: string) => {
    try {
      const summaryPrompt = `Genera un resumen breve y conciso del siguiente contenido en español:

${content}

Resumen:`;

      const response = await toolLLM.invoke(summaryPrompt);
      return response.content as string;
    } catch (error) {
      console.error('Error generating summary:', error);
      return "Error generating summary.";
    }
  },
});

// Tool for language detection and translation
export const languageTool = new DynamicTool({
  name: "language_detection",
  description: "Detect the language of the input text and provide translation if needed.",
  func: async (text: string) => {
    try {
      const languagePrompt = `Analiza el siguiente texto y responde en formato JSON:
        - Detecta el idioma
        - Si no es español, proporciona una traducción al español
        - Si es español, indica que no necesita traducción

        Texto: "${text}"

        Respuesta en formato JSON:`;

      const response = await toolLLM.invoke(languagePrompt);
      return response.content as string;
    } catch (error) {
      console.error('Error in language detection:', error);
      return "Error detecting language.";
    }
  },
});

// Tool for sentiment analysis
export const sentimentAnalysisTool = new DynamicTool({
  name: "sentiment_analysis",
  description: "Analyze the sentiment of the user's message to provide more empathetic responses.",
  func: async (text: string) => {
    try {
      const sentimentPrompt = `Analiza el sentimiento del siguiente texto y responde en formato JSON:
      - Determina si es positivo, negativo, neutral, o mixto
      - Identifica la emoción principal (alegría, tristeza, enojo, miedo, sorpresa, etc.)
      - Proporciona una puntuación de -1 (muy negativo) a 1 (muy positivo)

      Texto: "${text}"

      Respuesta en formato JSON:`;

      const response = await toolLLM.invoke(sentimentPrompt);
      return response.content as string;
    } catch (error) {
      console.error('Error in sentiment analysis:', error);
      return "Error analyzing sentiment.";
    }
  },
});

// Tool for session management
export const sessionManagementTool = new DynamicTool({
  name: "session_management",
  description: "Manage user sessions, track session numbers, and handle session transitions.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:action:value"
      const [userId, action, value] = input.split(':');

      if (!userId || !action) {
        return "Invalid format. Expected: userId:action:value";
      }

      switch (action) {
        case 'increment_session':
          const currentSession = await storage.getUserContext(userId, 'sessionNumber');
          const newSessionNumber = (parseInt(currentSession || '0') + 1).toString();
          await storage.setUserContext(userId, 'sessionNumber', newSessionNumber);
          return `Session number incremented to ${newSessionNumber}`;

        case 'get_session_number':
          const sessionNumber = await storage.getUserContext(userId, 'sessionNumber');
          return sessionNumber || '1';

        case 'set_session_data':
          const [key, dataValue] = value.split('=');
          if (key && dataValue) {
            await storage.setUserContext(userId, key, dataValue);
            return `Session data ${key} set to ${dataValue}`;
          }
          return "Invalid session data format";

        default:
          return `Unknown action: ${action}`;
      }
    } catch (error) {
      console.error('Error in session management:', error);
      return "Error managing session.";
    }
  },
});

// Tool for extracting relationships from text
export const extractRelationshipsTool = new DynamicTool({
  name: "extract_relationships",
  description: "Extract relationships between people or entities mentioned in text.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:text"
      const [userId] = input.split(':', 1);
      const remainingText = input.substring(userId.length + 1);

      if (!userId || !remainingText) {
        return "Invalid format. Expected: userId:text containing relationships";
      }

      const relationshipPrompt = `Extrae las relaciones entre personas o entidades del siguiente texto.
      Responde en formato JSON con un array de objetos que contengan:
      - entity1: La primera entidad
      - relationship: La relación de entity1 hacia entity2
      - entity2: La segunda entidad

      Por ejemplo, si el texto dice "María es la madre de Juan", el resultado sería:
      [{"entity1":"María","relationship":"madre","entity2":"Juan"}]

      Texto: "${remainingText}"

      Respuesta SOLO en formato JSON:`;

      const response = await toolLLM.invoke(relationshipPrompt);
      const content = response.content as string;

      // Extract JSON from the response - using a more compatible regex
      const jsonMatch = content.match(/\[\s*\{[\s\S]*\}\s*\]/);
      if (!jsonMatch) {
        return "No relationships found or could not parse response.";
      }

      try {
        const relationships = JSON.parse(jsonMatch[0]);

        // Store relationships in database
        for (const rel of relationships) {
          await storage.addRelationship(
            userId,
            rel.entity1,
            rel.relationship,
            rel.entity2
          );
        }

        return `Successfully extracted and stored ${relationships.length} relationships.`;
      } catch (jsonError) {
        console.error('Error parsing JSON from relationship extraction:', jsonError);
        return "Error parsing extracted relationships.";
      }
    } catch (error) {
      console.error('Error extracting relationships:', error);
      return "Error extracting relationships.";
    }
  },
});

// Tool for inferring relationships between entities
export const inferRelationshipTool = new DynamicTool({
  name: "infer_relationship",
  description: "Infer the relationship between two entities based on stored relationship data.",
  func: async (input: string) => {
    try {
      // Expected format: "userId:entity1:entity2"
      const [userId, entity1, entity2] = input.split(':');

      if (!userId || !entity1 || !entity2) {
        return "Invalid format. Expected: userId:entity1:entity2";
      }

      const relationship = await storage.inferRelationship(userId, entity1, entity2);

      if (relationship) {
        return `Relationship from ${entity1} to ${entity2}: ${relationship}`;
      } else {
        return `No relationship found between ${entity1} and ${entity2}.`;
      }
    } catch (error) {
      console.error('Error inferring relationship:', error);
      return "Error inferring relationship.";
    }
  },
});

// Tool for getting all relationships for a user
export const getRelationshipsTool = new DynamicTool({
  name: "get_relationships",
  description: "Get all stored relationships for a user.",
  func: async (userId: string) => {
    try {
      const relationships = await storage.getRelationships(userId);

      if (relationships.length === 0) {
        return "No relationships stored for this user.";
      }

      const relationshipsText = relationships
        .map(r => `${r.entity1} ${r.relationship} ${r.entity2}`)
        .join('\n');

      return `Stored relationships:\n${relationshipsText}`;
    } catch (error) {
      console.error('Error getting relationships:', error);
      return "Error retrieving relationships.";
    }
  },
});

// Export all tools
export const langChainTools: Tool[] = [
  getUserContextTool,
  setUserContextTool,
  searchSimilarConversationsTool,
  getChatHistoryTool,
  generateSummaryTool,
  languageTool,
  sentimentAnalysisTool,
  sessionManagementTool,
  extractRelationshipsTool,
  inferRelationshipTool,
  getRelationshipsTool,
];

// Tool executor utility
export class ToolExecutor {
  private tools: Map<string, Tool>;

  constructor() {
    this.tools = new Map();
    langChainTools.forEach(tool => {
      this.tools.set(tool.name, tool);
    });
  }

  async executeTool(toolName: string, input: string): Promise<string> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new Error(`Tool ${toolName} not found`);
    }
    
    try {
      return await tool.invoke(input);
    } catch (error) {
      console.error(`Error executing tool ${toolName}:`, error);
      throw error;
    }
  }

  getAvailableTools(): string[] {
    return Array.from(this.tools.keys());
  }

  getToolDescriptions(): Record<string, string> {
    const descriptions: Record<string, string> = {};
    this.tools.forEach((tool, name) => {
      descriptions[name] = tool.description;
    });
    return descriptions;
  }
}

export const toolExecutor = new ToolExecutor(); 