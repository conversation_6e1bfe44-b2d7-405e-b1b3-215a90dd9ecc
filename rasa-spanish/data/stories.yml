# version: "3.1"

# stories:
#   - story: saludo y despedida simple
#     steps:
#       - intent: saludo
#       - action: utter_saludo
#       - intent: despedida
#       - action: utter_despedida

#   - story: pedir información general
#     steps:
#       - intent: saludo
#       - action: utter_saludo
#       - intent: pedir_informacion
#       - action: utter_informacion_general
#       - intent: pregunta_general
#       - action: utter_pregunta_general
#       - intent: despedida
#       - action: utter_despedida

#   - story: reporte de problema y feedback
#     steps:
#       - intent: reportar_problema
#       - action: utter_agradecer_reporte
#       - intent: dar_feedback
#       - action: utter_agradecer_feedback
#       - intent: despedida
#       - action: utter_despedida

#   - story: consultar estado de una solicitud
#     steps:
#       - intent: consultar_estado
#       - action: utter_pedir_datos_estado
#       - intent: afirmar
#       - action: utter_confirmacion_positiva
#       - action: action_verificar_estado
#       - action: utter_estado_actual
#       - intent: despedida
#       - action: utter_despedida

#   - story: chitchat casual
#     steps:
#       - intent: chitchat
#       - action: utter_respuesta_chitchat
#       - intent: despedida
#       - action: utter_despedida

#   - story: manejo de afirmaciones y negaciones
#     steps:
#       - intent: pedir_informacion
#       - action: utter_pedir_confirmacion
#       - intent: afirmar
#       - action: utter_confirmacion_positiva
#       - intent: negar
#       - action: utter_confirmacion_negativa
#       - intent: despedida
#       - action: utter_despedida

#   - story: ayuda y repetición
#     steps:
#       - intent: pedir_ayuda
#       - action: utter_ofrecer_ayuda
#       - intent: pedir_repeticion
#       - action: utter_repetir_ultimo
#       - intent: despedida
#       - action: utter_despedida

#   - story: hacer reserva exitosa
#     steps:
#       - intent: hacer_reserva
#       - action: utter_pedir_detalles_reserva
#       - intent: afirmar
#       - action: utter_confirmacion_positiva
#       - action: action_realizar_reserva
#       - action: utter_confirmar_reserva
#       - intent: despedida
#       - action: utter_despedida

#   - story: cancelar reserva
#     steps:
#       - intent: cancelar_reserva
#       - action: utter_pedir_confirmacion_cancelacion
#       - intent: afirmar
#       - action: utter_confirmacion_positiva
#       - action: action_cancelar_reserva
#       - action: utter_confirmar_cancelacion
#       - intent: despedida
#       - action: utter_despedida

#   - story: manejo out_of_scope y fallback
#     steps:
#       - intent: out_of_scope
#       - action: utter_no_entendido
#       - intent: nlu_fallback
#       - action: utter_fallback
#       - intent: despedida
#       - action: utter_despedida


version: "3.1"

stories:

- story: basic greeting and support
  steps:
  - intent: greet
  - action: utter_greet
  - intent: feeling_depressed
  - action: utter_depression_support
  - intent: need_support
  - action: utter_professional_help

- story: anxiety management conversation
  steps:
  - intent: feeling_anxious
  - action: utter_anxiety_support
  - intent: affirm
  - action: utter_breathing_exercise
  - intent: affirm
  - action: utter_coping_strategies

- story: work stress guidance
  steps:
  - intent: work_stress
  - action: utter_work_stress_support
  - intent: need_support
  - action: utter_coping_strategies
  - intent: professional_help
  - action: utter_professional_help

- story: relationship support flow
  steps:
  - intent: relationship_issues
  - action: utter_relationship_support
  - intent: feeling_sad
  - action: utter_coping_strategies
  - intent: professional_help
  - action: utter_professional_help

- story: panic attack immediate help
  steps:
  - intent: panic_attack
  - action: utter_panic_attack_help
  - action: utter_breathing_exercise
  - intent: feeling_anxious
  - action: utter_anxiety_support

- story: depression to professional help
  steps:
  - intent: feeling_depressed
  - action: utter_depression_support
  - intent: professional_help
  - action: utter_professional_help
  - action: action_provide_resources

- story: coping strategies request
  steps:
  - intent: coping_strategies
  - action: utter_coping_strategies
  - intent: affirm
  - action: utter_self_care_tips

- story: breathing exercise request
  steps:
  - intent: breathing_exercises
  - action: utter_breathing_exercise
  - intent: affirm
  - action: utter_coping_strategies

- story: general support conversation
  steps:
  - intent: greet
  - action: utter_greet
  - intent: need_support
  - action: action_mood_tracking
  - intent: affirm
  - action: utter_coping_strategies

- story: positive goodbye
  steps:
  - intent: greet
  - action: utter_greet
  - intent: affirm
  - action: utter_self_care_tips
  - intent: goodbye
  - action: utter_goodbye

- story: out of scope handling
  steps:
  - intent: out_of_scope
  - action: utter_out_of_scope
  - intent: feeling_anxious
  - action: utter_anxiety_support

- story: bot challenge response
  steps:
  - intent: bot_challenge
  - action: utter_bot_challenge
  - intent: need_support
  - action: utter_professional_help

- story: multiple emotional states
  steps:
  - intent: feeling_depressed
  - action: utter_depression_support
  - intent: feeling_anxious
  - action: utter_anxiety_support
  - intent: professional_help
  - action: utter_professional_help

- story: work stress to depression
  steps:
  - intent: work_stress
  - action: utter_work_stress_support
  - intent: feeling_depressed
  - action: utter_depression_support
  - intent: professional_help
  - action: utter_professional_help

