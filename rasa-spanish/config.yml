# The config recipe.
# https://rasa.com/docs/rasa/model-configuration/
#recipe: default.v1

# The assistant project unique identifier
assistant_id: rasa-spanish-assistant

# Configuration for Rasa NLU.
# https://rasa.com/docs/rasa/nlu/components/
#language: es

#pipeline: null
# # No configuration for the NLU pipeline was provided. The following default pipeline was used to train your model.
# # If you'd like to customize it, uncomment and adjust the pipeline.
# # See https://rasa.com/docs/rasa/tuning-your-model for more information.
#   - name: WhitespaceTokenizer
#   - name: RegexFeaturizer
#   - name: LexicalSyntacticFeaturizer
#   - name: CountVectorsFeaturizer
#   - name: CountVectorsFeaturizer
#     analyzer: char_wb
#     min_ngram: 1
#     max_ngram: 4
#   - name: DIETClassifier
#     epochs: 100
#     constrain_similarities: true
#   - name: EntitySynonymMapper
#   - name: ResponseSelector
#     epochs: 100
#     constrain_similarities: true
#   - name: FallbackClassifier
#     threshold: 0.3
#     ambiguity_threshold: 0.1

# Configuration for Rasa Core.
# https://rasa.com/docs/rasa/core/policies/
#policies: null
# # No configuration for policies was provided. The following default policies were used to train your model.
# # If you'd like to customize them, uncomment and adjust the policies.
# # See https://rasa.com/docs/rasa/policies for more information.
#   - name: MemoizationPolicy
#   - name: RulePolicy
#   - name: UnexpecTEDIntentPolicy
#     max_history: 5
#     epochs: 100
#   - name: TEDPolicy
#     max_history: 5
#     epochs: 100
#     constrain_similarities: true



recipe: default.v1
language: es

# language: en
pipeline:
  - name: WhitespaceTokenizer
  - name: RegexFeaturizer
  - name: LexicalSyntacticFeaturizer
  - name: CountVectorsFeaturizer
  - name: DIETClassifier
    epochs: 100
    constrain_similarities: true
  - name: FallbackClassifier
    threshold: 0.7
    ambiguity_threshold: 0.1
policies:
  - name: MemoizationPolicy
  - name: RulePolicy
    core_fallback_threshold: 0.3
    core_fallback_action_name: "action_fallback_openai"
    enable_fallback_prediction: True
  - name: UnexpecTEDIntentPolicy
  - name: TEDPolicy
    max_history: 5
    epochs: 100
# # No configuration for policies was provided. The following default policies were used to train your model.
# # If you'd like to customize them, uncomment and adjust the policies.
# # See https://rasa.com/docs/rasa/policies for more information.
#   - name: MemoizationPolicy
#   - name: RulePolicy
#   - name: UnexpecTEDIntentPolicy
#     max_history: 5
#     epochs: 100
#   - name: TEDPolicy
#     max_history: 5
#     epochs: 100
#     constrain_similarities: true

# version: "3.1"
# recipe: default.v1
# language: es

# # Clean pipeline configuration for Spanish psychology assistant
# pipeline:
#   # Tokenization optimized for Spanish
#   - name: WhitespaceTokenizer
#   - name: RegexFeaturizer
#   - name: LexicalSyntacticFeaturizer
  
#   # Feature extraction with n-grams for better Spanish understanding
#   - name: CountVectorsFeaturizer
#     analyzer: word
#     min_ngram: 1
#     max_ngram: 2
#     max_features: 10000
#   - name: CountVectorsFeaturizer
#     analyzer: char_wb
#     min_ngram: 1
#     max_ngram: 4
#     max_features: 5000
  
#   # Enhanced DIET classifier for psychology domain
#   - name: DIETClassifier
#     epochs: 100
#     constrain_similarities: true
#     model_confidence: "softmax"
#     loss_type: "cross_entropy"
#     ranking_length: 10
#     dense_dimension:
#       text: 256
#       label: 64
#     number_of_transformer_layers: 1
#     transformer_size: 128
#     use_masked_language_model: true
#     BILOU_flag: true
    
#   # Entity handling
#   - name: EntitySynonymMapper
  
#   # Response selection for varied responses
#   - name: ResponseSelector
#     epochs: 50
#     constrain_similarities: true
#     model_confidence: "softmax"
    
#   # Fallback classification - important for safety
#   - name: FallbackClassifier
#     threshold: 0.5
#     ambiguity_threshold: 0.1

# policies:
#   # Exact pattern memorization
#   - name: MemoizationPolicy
#     max_history: 3
    
#   # Rule-based responses (critical for crisis)
#   - name: RulePolicy
#     core_fallback_threshold: 0.3
#     core_fallback_action_name: "action_fallback_openai"
#     enable_fallback_prediction: true
    
#   # Unexpected intent handling
#   - name: UnexpecTEDIntentPolicy
#     max_history: 5
#     epochs: 50
#     constrain_similarities: true
    
#   # Main conversation policy
#   - name: TEDPolicy
#     max_history: 5
#     epochs: 100
#     constrain_similarities: true
#     loss_type: "cross_entropy"
#     ranking_length: 10
#     model_confidence: "softmax"
#     number_of_transformer_layers: 1
#     transformer_size: 128
#     dense_dimension: 128
#     regularization_constant: 0.001
#     drop_rate: 0.2
#     drop_rate_attention: 0.0