from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet, FollowupAction, UserUtteranceReverted
import random
import logging

logger = logging.getLogger(__name__)

class ActionAssessCrisis(Action):
    """Assess if user is in crisis and set appropriate slots"""
    
    def name(self) -> Text:
        return "action_assess_crisis"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        intent = tracker.latest_message['intent'].get('name')
        text = tracker.latest_message.get('text', '').lower()
        
        # Crisis keywords in Spanish
        crisis_keywords = [
            'suicidio', 'suicidarme', 'matarme', 'morir', 'muerte',
            'acabar', 'terminar', 'lastimar', 'daño', 'dolor',
            'no aguanto', 'no puedo más', 'desaparecer', 'irme'
        ]
        
        crisis_intents = ['suicidal_thoughts', 'self_harm', 'crisis_help_needed']
        
        # Check for crisis indicators
        is_crisis = (
            intent in crisis_intents or 
            any(keyword in text for keyword in crisis_keywords)
        )
        
        if is_crisis:
            logger.warning(f"Crisis detected - Intent: {intent}, Text: {text}")
            return [
                SlotSet("crisis_mode", True),
                SlotSet("support_needed", True)
            ]
        
        return [SlotSet("crisis_mode", False)]

class ActionEmergencyProtocol(Action):
    """Activate emergency protocol with detailed resources"""
    
    def name(self) -> Text:
        return "action_emergency_protocol"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        emergency_message = """
🆘 **PROTOCOLO DE EMERGENCIA ACTIVADO** 🆘

Tu seguridad es la prioridad absoluta. CONTACTA INMEDIATAMENTE:

📞 **LÍNEAS DE CRISIS 24/7:**
• Teléfono de la Esperanza: 717 003 717
• Línea de Crisis Nacional: 024 (gratuita)
• Cruz Roja Española: 902 22 22 92

🚨 **EMERGENCIAS MÉDICAS:**
• Emergencias: 112
• SAMUR (Madrid): 915 80 87 34

🏥 **ACUDE AHORA MISMO A:**
• Urgencias del hospital más cercano
• Centro de Salud Mental de tu zona
• Servicio de Urgencias Psiquiátricas

💚 **RECUERDA:**
- Tu vida tiene un valor incalculable
- Esta crisis es temporal
- Hay profesionales esperando para ayudarte
- No estás solo/a en esto

Por favor, no esperes. Busca ayuda AHORA.
        """
        
        dispatcher.utter_message(text=emergency_message)
        
        # Log crisis event
        logger.critical("Emergency protocol activated for user")
        
        return []

class ActionProvideResources(Action):
    """Provide comprehensive mental health resources"""
    
    def name(self) -> Text:
        return "action_provide_resources"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        crisis_mode = tracker.get_slot("crisis_mode")
        
        if crisis_mode:
            # Crisis resources already provided by emergency protocol
            return []
        
        resources = """
🏥 **RECURSOS DE SALUD MENTAL EN ESPAÑA**

**🆘 LÍNEAS DE APOYO:**
• Teléfono de la Esperanza: 717 003 717
• Línea de Crisis: 024 (gratuita 24h)
• ANAR (menores): 900 20 20 10
• Violencia de Género: 016

**👨‍⚕️ AYUDA PROFESIONAL:**
• Colegio Oficial de Psicólogos: cop.es
• Centros de Salud Mental (derivación médico familia)
• Psicólogos sin Fronteras: psicologossinfronteras.org
• Asociación Española de Psicología Clínica: aepc.es

**💻 RECURSOS ONLINE:**
• Psychology Today España: psychologytoday.com/es
• Doctoralia: doctoralia.es
• Top Doctors: topdoctors.es

**📱 APPS DE AUTOAYUDA:**
• Calm: Meditación y relajación
• Headspace: Mindfulness
• Sanvello: Manejo de ansiedad y depresión
• MindShift: Técnicas para ansiedad

**🏛️ RECURSOS PÚBLICOS:**
• Centros de Salud Mental Comunitarios
• Unidades de Salud Mental Infantil y Juvenil
• Hospitales de Día
• Centros de Rehabilitación Psicosocial

**💰 AYUDA ECONÓMICA:**
• Colegios Profesionales (consultas reducidas)
• Centros universitarios con prácticas supervisadas
• ONGs con servicios gratuitos
• Mutuas y seguros médicos
        """
        
        dispatcher.utter_message(text=resources)
        return []

class ActionMoodTracking(Action):
    """Simple mood tracking and assessment"""
    
    def name(self) -> Text:
        return "action_mood_tracking"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        mood_questions = [
            "En una escala del 1 al 10, ¿cómo calificarías tu estado de ánimo hoy? (1 = muy bajo, 10 = excelente)",
            "¿Cómo te has sentido durante los últimos días? ¿Has notado algún patrón en tu estado de ánimo?",
            "¿Hay situaciones específicas que han afectado tu bienestar emocional recientemente?",
            "¿Qué actividades te han ayudado a sentirte mejor últimamente?",
            "¿Has dormido bien? ¿Cómo está tu apetito? Estos factores pueden afectar mucho nuestro estado de ánimo."
        ]
        
        question = random.choice(mood_questions)
        dispatcher.utter_message(text=f"Vamos a hacer un seguimiento de tu bienestar emocional. {question}")
        
        return []

class ActionPersonalizedResponse(Action):
    """Generate personalized responses based on user's emotional state"""
    
    def name(self) -> Text:
        return "action_personalized_response"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        intent = tracker.latest_message['intent'].get('name')
        emotion_state = tracker.get_slot("emotion_state")
        severity_level = tracker.get_slot("severity_level")
        
        # Personalized responses based on context
        responses = {
            'work_stress': [
                "El estrés laboral puede ser abrumador. Es importante recordar que tu valor como persona no se define por tu trabajo.",
                "Entiendo lo difícil que puede ser el ambiente laboral. Establecer límites saludables es crucial para tu bienestar.",
                "El burnout es real y válido. Tu salud mental es más importante que cualquier trabajo."
            ],
            
            'relationship_issues': [
                "Las relaciones pueden ser complicadas. Recuerda que la comunicación abierta y honesta es clave.",
                "Los conflictos en las relaciones son normales, pero es importante que te sientas respetado/a y valorado/a.",
                "Tu bienestar emocional es importante. No debes sacrificar tu salud mental por mantener una relación."
            ],
            
            'family_problems': [
                "Los problemas familiares pueden ser especialmente dolorosos porque involucran personas muy cercanas.",
                "Cada familia es diferente, y está bien establecer límites saludables incluso con la familia.",
                "No puedes cambiar a otros, pero sí puedes trabajar en cómo respondes a las situaciones familiares."
            ],
            
            'financial_stress': [
                "Las preocupaciones financieras pueden generar mucha ansiedad. Es importante recordar que hay recursos y ayuda disponibles.",
                "El estrés financiero afecta a muchas personas. No estás solo/a en esto.",
                "Aunque la situación económica sea difícil, hay estrategias para manejar la ansiedad que esto genera."
            ]
        }
        
        # Get appropriate response
        intent_responses = responses.get(intent, [
            "Gracias por confiar en mí y compartir lo que sientes. Cada experiencia es única y válida.",
            "Entiendo que estás pasando por un momento difícil. Estoy aquí para apoyarte.",
            "Tus sentimientos son completamente válidos. Es normal sentirse así en situaciones complicadas."
        ])
        
        response = random.choice(intent_responses)
        
        # Add severity-based context
        if severity_level == "severo" or severity_level == "extremo":
            response += " Given the intensity of what you're experiencing, I strongly recommend speaking with a mental health professional."
        
        dispatcher.utter_message(text=response)
        
        return []

class ActionFallbackOpenai(Action):
    """Enhanced fallback action with safety measures"""
    
    def name(self) -> Text:
        return "action_fallback_openai"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        user_message = tracker.latest_message.get('text', '').lower()
        
        # Check for crisis keywords even in fallback
        crisis_keywords = ['suicidio', 'morir', 'matarme', 'acabar conmigo', 'no puedo más']
        
        if any(keyword in user_message for keyword in crisis_keywords):
            dispatcher.utter_message(
                text="Me preocupa lo que acabas de compartir. Tu seguridad es lo más importante."
            )
            return [
                SlotSet("crisis_mode", True),
                FollowupAction("action_emergency_protocol")
            ]
        
        # Safe fallback responses
        fallback_responses = [
            "Entiendo que cada situación es única. Aunque no pueda abordar ese tema específico, estoy aquí para escucharte y apoyarte emocionalmente.",
            "Gracias por compartir conmigo. Aunque mi especialidad es el apoyo emocional, puedo ayudarte a procesar cómo te sientes respecto a esa situación.",
            "Aprecio tu confianza. Si bien no puedo ayudar con todos los temas, sí puedo ofrecerte apoyo emocional y recursos de salud mental.",
            "Cada experiencia es válida. ¿Hay algo relacionado con tu bienestar emocional en lo que pueda ayudarte?",
            "Entiendo que hay muchos aspectos en la vida que pueden afectarnos. ¿Te gustaría hablar sobre cómo te sientes respecto a lo que mencionas?"
        ]
        
        response = random.choice(fallback_responses)
        dispatcher.utter_message(text=response)
        
        # Offer resources
        dispatcher.utter_message(
            text="Si necesitas ayuda especializada o tienes una emergencia, recuerda que puedes contactar:\n"
                 "• Teléfono de la Esperanza: 717 003 717\n"
                 "• Línea de Crisis: 024"
        )
        
        return []
