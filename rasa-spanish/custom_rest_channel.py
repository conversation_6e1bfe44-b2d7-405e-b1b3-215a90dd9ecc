from rasa.core.channels.rest import RestInput
from rasa.shared.core.events import SlotSet
from typing import Text, Optional, Dict, Any

class CustomRestInput(RestInput):
    async def process_message(
        self, request: Dict[Text, Any], sender_id: Optional[Text] = None
    ) -> None:
        # Extract user_id from metadata if available
        metadata = request.get("metadata", {})
        user_id = metadata.get("user_id")

        # Call the parent method to continue normal processing
        await super().process_message(request, sender_id)

        # If user_id found, set slot for that sender
        if user_id:
            # Set slot 'user_id' for this sender
            await self.on_new_message(
                self._collector,
                sender_id,
                message=None,
                input_channel=self.name(),
                metadata={},
                additional_events=[SlotSet("user_id", user_id)],
            )
