# version: "3.1"

# intents:
#   - saludo
#   - despedida
#   - pedir_informacion
#   - reportar_problema
#   - consultar_estado
#   - chitchat
#   - pregunta_general
#   - afirmar
#   - negar
#   - pedir_repeticion
#   - pedir_ayuda
#   - hacer_reserva
#   - cancelar_reserva
#   - dar_feedback
#   - out_of_scope
#   - nlu_fallback

# entities:
#   - fecha_reserva
#   - numero_pedido
#   - nombre_usuario
#   - problema_descripcion

# slots:
#   fecha_reserva:
#     type: text
#     influence_conversation: false
#     mappings:
#       - type: from_text
#   numero_pedido:
#     type: text
#     influence_conversation: false
#     mappings:
#       - type: from_text
#   nombre_usuario:
#     type: text
#     influence_conversation: false
#     mappings:
#       - type: from_text
#   problema_descripcion:
#     type: text
#     influence_conversation: false
#     mappings:
#       - type: from_text

# responses:
#   utter_saludo:
#     - text: "¡Hola! ¿En qué puedo ayudarte hoy?"
#   utter_despedida:
#     - text: "¡Hasta luego! Que tengas un buen día."
#   utter_informacion_general:
#     - text: "C<PERSON><PERSON>, ¿sobre qué tema necesitas información?"
#   utter_pregunta_general:
#     - text: "¿Podrías especificar tu pregunta, por favor?"
#   utter_agradecer_reporte:
#     - text: "Gracias por reportar el problema. Lo revisaremos pronto."
#   utter_agradecer_feedback:
#     - text: "Agradecemos mucho tus comentarios."
#   utter_pedir_datos_estado:
#     - text: "Por favor, indícame el número de tu pedido o solicitud."
#   utter_estado_actual:
#     - text: "El estado actual de tu solicitud es: {{ estado }}."
#   utter_respuesta_chitchat:
#     - text: "¡Qué bueno que estés charlando conmigo! ¿En qué más te puedo ayudar?"
#   utter_pedir_confirmacion:
#     - text: "¿Confirmas que quieres proceder?"
#   utter_confirmacion_positiva:
#     - text: "Perfecto, continúo con la operación."
#   utter_confirmacion_negativa:
#     - text: "Entendido, cancelamos la operación."
#   utter_ofrecer_ayuda:
#     - text: "Estoy aquí para ayudarte. ¿Qué necesitas?"
#   utter_repetir_ultimo:
#     - text: "Claro, te repito la información anterior."
#   utter_pedir_detalles_reserva:
#     - text: "Por favor, dime la fecha y hora para tu reserva."
#   utter_confirmar_reserva:
#     - text: "Tu reserva ha sido confirmada. ¡Gracias!"
#   utter_pedir_confirmacion_cancelacion:
#     - text: "¿Seguro que quieres cancelar la reserva?"
#   utter_confirmar_cancelacion:
#     - text: "Tu reserva ha sido cancelada."
#   utter_no_entendido:
#     - text: "Disculpa, no entendí lo que quisiste decir."
#   utter_fallback:
#     - text: "No estoy seguro de cómo ayudarte con eso. ¿Quieres intentar de nuevo?"

# forms:
#   reserva_form:
#     required_slots:
#       - fecha_reserva
#       - numero_pedido

# actions:
#   - action_verificar_estado
#   - action_realizar_reserva
#   - action_cancelar_reserva
#   - validate_reserva_form
#   - action_fallback_openai

version: "3.1"

intents:
  # Basic interactions
  - greet
  - goodbye
  - affirm
  - deny
  - bot_challenge
  - out_of_scope
  
  # Emotional states - Depression & Mood
  - feeling_depressed
  - feeling_sad
  - feeling_hopeless
  - feeling_empty
  
  # Anxiety & Stress
  - feeling_anxious
  - feeling_stressed
  - feeling_overwhelmed
  - panic_attack
  - worry_constantly
  
  # Crisis situations
  - suicidal_thoughts
  - self_harm
  - crisis_help_needed
  
  # Support & Coping
  - need_support
  - need_someone_to_talk
  - coping_strategies
  - breathing_exercises
  
  # Professional help
  - professional_help
  - find_therapist
  
  # Life situations
  - work_stress
  - relationship_issues
  
  # Information seeking
  - ask_about_depression
  - ask_about_anxiety
  - mental_health_resources

entities:
  - emotion
  - severity
  - duration

slots:
  emotion_state:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: emotion
  
  severity_level:
    type: categorical
    values:
    - leve
    - moderado
    - severo
    - extremo
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: severity
  
  support_needed:
    type: bool
    influence_conversation: true
    initial_value: false
    mappings:
    - type: from_intent
      intent: need_support
      value: true
    - type: from_intent
      intent: need_someone_to_talk
      value: true
  
  crisis_mode:
    type: bool
    initial_value: false
    influence_conversation: true
    mappings:
    - type: custom
  
  professional_help_requested:
    type: bool
    initial_value: false
    influence_conversation: true
    mappings:
    - type: from_intent
      intent: professional_help
      value: true
    - type: from_intent
      intent: find_therapist
      value: true

responses:
  utter_greet:
  - text: "Hola! Soy tu asistente de apoyo psicológico. Estoy aquí para escucharte y ayudarte. ¿Cómo te sientes hoy?"
  - text: "Hola, me alegra que estés aquí. Soy un asistente especializado en apoyo emocional. ¿En qué puedo ayudarte hoy?"
  - text: "Bienvenido/a! Estoy aquí para brindarte apoyo y comprensión. ¿Qué tal te encuentras?"

  utter_goodbye:
  - text: "Cuídate mucho. Recuerda que buscar ayuda es valiente y que siempre puedes volver a hablar conmigo. Hasta pronto!"
  - text: "Ha sido un gusto ayudarte. Tu bienestar es importante, no dudes en contactarme cuando lo necesites. Que tengas un buen día!"
  - text: "Espero haberte sido de ayuda. Recuerda que no estás solo/a en esto. Nos vemos pronto!"

  utter_depression_support:
  - text: "Entiendo que te sientes deprimido/a, y quiero que sepas que tus sentimientos son completamente válidos. La depresión es una condición real y tratable. ¿Hace cuánto tiempo te sientes así?"
  - text: "Lamento que estés pasando por esto. La depresión puede hacer que todo parezca sin esperanza, pero hay formas de sentirse mejor. ¿Puedes contarme un poco más sobre lo que estás experimentando?"

  utter_anxiety_support:
  - text: "La ansiedad puede ser realmente abrumadora y agotadora. Es completamente comprensible que te sientas así. ¿Hay situaciones específicas que desencadenan tu ansiedad?"
  - text: "Entiendo lo difícil que puede ser lidiar con la ansiedad. Tu cuerpo y mente están tratando de protegerte, pero puede resultar agobiante. ¿Te gustaría que practiquemos algunas técnicas de relajación?"

  utter_crisis_response:
  - text: "IMPORTANTE: Me preocupa profundamente tu seguridad y bienestar. Lo que sientes es muy serio y necesitas ayuda profesional inmediata.\n\nCONTACTA AHORA:\n• Teléfono de la Esperanza: 717 003 717\n• Línea de Crisis: 024 (gratuita 24h)\n• Emergencias: 112\n\nTu vida tiene un valor inmenso. Por favor, busca ayuda ahora mismo."

  utter_panic_attack_help:
  - text: "Entiendo que estás teniendo un ataque de pánico. Vamos a trabajar juntos para que te sientas mejor. Primero, recuerda que esto pasará y que estás seguro/a."

  utter_breathing_exercise:
  - text: "Vamos a hacer un ejercicio de respiración juntos. Sigue mis instrucciones:\n\n1. Inhala lentamente por la nariz contando hasta 4... 1, 2, 3, 4\n2. Mantén la respiración contando hasta 4... 1, 2, 3, 4\n3. Exhala lentamente por la boca contando hasta 6... 1, 2, 3, 4, 5, 6\n\nRepite esto varias veces. Te ayudará a calmarte."

  utter_coping_strategies:
  - text: "Aquí tienes algunas estrategias de afrontamiento que pueden ayudarte:\n\n• Mindfulness: Concéntrate en el momento presente\n• Ejercicio: Incluso una caminata corta puede ayudar\n• Conexión social: Habla con amigos o familiares de confianza\n• Rutina: Mantén horarios regulares de sueño y comidas\n• Autocuidado: Dedica tiempo a actividades que disfrutes\n• Journaling: Escribe tus pensamientos y sentimientos"

  utter_professional_help:
  - text: "Es muy sabio considerar ayuda profesional. Un psicólogo o psiquiatra puede ofrecerte herramientas personalizadas y apoyo especializado. ¿Te gustaría información sobre cómo encontrar un profesional en tu área?"

  utter_work_stress_support:
  - text: "El estrés laboral es muy común y puede afectar significativamente tu bienestar. Es importante establecer límites y encontrar formas saludables de manejar la presión. ¿Qué aspectos específicos del trabajo te causan más estrés?"

  utter_relationship_support:
  - text: "Las relaciones pueden ser complicadas y los conflictos son normales. Lo importante es la comunicación saludable y el respeto mutuo. ¿Te gustaría hablar sobre estrategias de comunicación o necesitas apoyo para procesar tus sentimientos?"

  utter_self_care_tips:
  - text: "El autocuidado es fundamental para tu bienestar mental:\n\n• Cuidado físico: Duerme bien, come saludable, haz ejercicio\n• Actividades placenteras: Dedica tiempo a hobbies que disfrutes\n• Límites: Aprende a decir 'no' cuando sea necesario\n• Crecimiento personal: Lee, aprende algo nuevo\n• Apoyo social: Mantén conexiones significativas\n• Relajación: Practica técnicas de relajación regularmente"

  utter_bot_challenge:
  - text: "Soy un asistente de inteligencia artificial especializado en apoyo psicológico. Aunque no soy un terapeuta humano, estoy aquí para escucharte, ofrecerte apoyo emocional y proporcionarte recursos útiles."

  utter_out_of_scope:
  - text: "Entiendo tu pregunta, pero como asistente de apoyo psicológico, me enfoco en temas relacionados con bienestar emocional y salud mental. ¿Hay algo relacionado con cómo te sientes que te gustaría compartir?"

  utter_default:
  - text: "Gracias por compartir eso conmigo. Cada persona y situación es única. ¿Podrías contarme un poco más sobre cómo te sientes respecto a eso?"
  - text: "Entiendo que cada experiencia es diferente. Estoy aquí para escucharte. ¿Hay algo específico sobre tu bienestar emocional en lo que pueda ayudarte?"

actions:
  # Custom actions
  - action_assess_crisis
  - action_provide_resources
  - action_mood_tracking
  - action_personalized_response
  - action_fallback_openai
  - action_emergency_protocol
  
  # Built-in actions
  - action_listen
  - action_restart
  - action_default_fallback
  
  # Response actions (these need to match the utter_ responses above)
  - utter_greet
  - utter_goodbye
  - utter_depression_support
  - utter_anxiety_support
  - utter_crisis_response
  - utter_panic_attack_help
  - utter_breathing_exercise
  - utter_coping_strategies
  - utter_professional_help
  - utter_work_stress_support
  - utter_relationship_support
  - utter_self_care_tips
  - utter_bot_challenge
  - utter_out_of_scope
  - utter_default

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
